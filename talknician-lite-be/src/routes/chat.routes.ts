import express from "express";
import { Request, Response } from "express";
import { authenticate } from "../middleware/auth.middleware";
import { chatService } from "../services/chat.service";
import { logger } from "../utils/logger";
import { prisma } from "@/config/database";
import { sseService } from "../services/sse.service";

const router: express.Router = express.Router();

// Apply auth middleware to all routes
router.use(authenticate);

/**
 * POST /api/chat/conversations
 * Create a new conversation
 */
router.post("/conversations", async (req: Request, res: Response) => {
  try {
    const { title } = req.body;
    const userId = req.user?.id;
    const organizationId = req.user?.organizations[0].id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    if (!organizationId) {
      return res.status(400).json({
        success: false,
        message: "Organization ID is required",
      });
    }

    const conversation = await chatService.createConversation(
      userId,
      organizationId,
      title
    );

    // Transform conversation to frontend format
    const formattedConversation = {
      id: conversation.id,
      title: conversation.title,
      createdAt: conversation.createdAt.toISOString(),
      updatedAt: conversation.updatedAt.toISOString(),
    };

    res.status(201).json({
      success: true,
      data: formattedConversation,
    });
  } catch (error) {
    logger.error("Error creating conversation:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create conversation",
    });
  }
});

/**
 * GET /api/chat/conversations
 * List user's conversations for an organization
 */
router.get("/conversations", async (req: Request, res: Response) => {
  try {
    const { organizationId } = req.query;
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    if (!organizationId) {
      return res.status(400).json({
        success: false,
        message: "Organization ID is required",
      });
    }

    const conversations = await chatService.listConversations(
      userId,
      organizationId as string
    );

    // Transform conversations to frontend format
    const formattedConversations = conversations.map((conv: any) => ({
      id: conv.id,
      title: conv.title,
      createdAt: conv.createdAt.toISOString(),
      updatedAt: conv.updatedAt.toISOString(),
    }));

    res.json({
      success: true,
      data: { conversations: formattedConversations },
    });
  } catch (error) {
    logger.error("Error listing conversations:", error);
    res.status(500).json({
      success: false,
      message: "Failed to list conversations",
    });
  }
});

/**
 * GET /api/chat/conversations/:id
 * Get conversation with messages
 */
router.get("/conversations/:id", async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    const conversation = await chatService.getConversation(id, userId);

    if (!conversation) {
      return res.status(404).json({
        success: false,
        message: "Conversation not found",
      });
    }

    // Transform messages to frontend format
    const messages =
      conversation.unifiedMessages?.map((msg) => ({
        id: msg.id,
        content: msg.content,
        role: msg.role.toLowerCase() as "user" | "assistant",
        createdAt: msg.createdAt.toISOString(),
        references: msg.references,
        annotations: msg.annotations,
      })) || [];

    res.json({
      success: true,
      data: {
        conversation: {
          id: conversation.id,
          title: conversation.title,
          createdAt: conversation.createdAt.toISOString(),
          updatedAt: conversation.updatedAt.toISOString(),
        },
        messages,
      },
    });
  } catch (error) {
    logger.error("Error getting conversation:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get conversation",
    });
  }
});

/**
 * POST /api/chat/conversations/:id/messages/stream
 * Send message with streaming response using SSE
 */
router.post(
  "/conversations/:id/messages/stream",
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const { message, settings } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "User not authenticated",
        });
      }

      if (!message) {
        return res.status(400).json({
          success: false,
          message: "Message is required",
        });
      }

      // Get organization ID from user context
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          organizationMemberships: {
            include: {
              organization: true,
            },
          },
        },
      });

      if (!user || user.organizationMemberships.length === 0) {
        return res.status(400).json({
          success: false,
          message: "User not associated with any organization",
        });
      }

      const organizationId = user.organizationMemberships[0].organization.id;

      // Create SSE connection
      sseService.createConnection(id, userId, res);

      // Stream the chat response
      await chatService.sendMessage(
        id,
        message,
        userId,
        organizationId,
        settings || {}
      );

      // Send end event and close connection
      sseService.sendEnd(id, userId);
    } catch (error) {
      logger.error("Error in streaming endpoint:", error);

      // If headers not sent yet, send error response
      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          message: "Failed to process message",
        });
      } else {
        // Send error through SSE service
        sseService.sendError(
          req.params.id,
          req.user?.id || "",
          "Internal server error",
          { error }
        );
      }
    } finally {
      // Close SSE connection
      sseService.closeConnection(`${req.params.id}-${req.user?.id}`);
    }
  }
);

/**
 * DELETE /api/chat/conversations/:id
 * Archive conversation
 */
router.delete("/conversations/:id", async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    await chatService.archiveConversation(id, userId);

    res.json({
      success: true,
      message: "Conversation archived successfully",
    });
  } catch (error) {
    logger.error("Error archiving conversation:", error);
    res.status(500).json({
      success: false,
      message: "Failed to archive conversation",
    });
  }
});

/**
 * GET /api/chat/settings
 * Get user's chat settings
 */
router.get("/settings", async (req: Request, res: Response) => {
  try {
    const { organizationId } = req.query;
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    if (!organizationId) {
      return res.status(400).json({
        success: false,
        message: "Organization ID is required",
      });
    }

    const settings = await chatService.getChatSettings(
      userId,
      organizationId as string
    );

    res.json({
      success: true,
      data: { settings },
    });
  } catch (error) {
    logger.error("Error getting chat settings:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get chat settings",
    });
  }
});

/**
 * PUT /api/chat/settings
 * Update user's chat settings
 */
router.put("/settings", async (req: Request, res: Response) => {
  try {
    const { organizationId, settings } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    if (!organizationId || !settings) {
      return res.status(400).json({
        success: false,
        message: "Organization ID and settings are required",
      });
    }

    const updatedSettings = await chatService.updateChatSettings(
      userId,
      organizationId,
      settings
    );

    res.json({
      success: true,
      data: { settings: updatedSettings },
    });
  } catch (error) {
    logger.error("Error updating chat settings:", error);
    res.status(500).json({
      success: false,
      message: "Failed to update chat settings",
    });
  }
});

export default router;
