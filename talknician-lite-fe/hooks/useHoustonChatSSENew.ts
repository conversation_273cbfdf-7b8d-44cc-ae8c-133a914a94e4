import { useState, useCallback, useRef } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { ChatMessage, ChatSettings, StreamingState } from "@/types/chat";

// Custom SSE client for POST requests
class CustomSSEClient {
  private controller: AbortController | null = null;
  private url: string;
  private options: any;

  constructor(url: string, options: any) {
    this.url = url;
    this.options = options;
    this.connect();
  }

  private async connect() {
    this.controller = new AbortController();

    try {
      const response = await fetch(this.url, {
        ...this.options,
        signal: this.controller.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      if (this.options.onopen) {
        this.options.onopen();
      }

      const reader = response.body?.getReader();
      const decoder = new TextDecoder();

      if (!reader) {
        throw new Error("No response body reader available");
      }

      let buffer = "";

      while (true) {
        const { done, value } = await reader.read();

        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split("\n");

        // Keep the last incomplete line in buffer
        buffer = lines.pop() || "";

        for (const line of lines) {
          if (line.startsWith("event: ")) {
            // Store event type for next data line
            continue;
          } else if (line.startsWith("data: ")) {
            const data = line.slice(6).trim();
            if (data && this.options.onmessage) {
              try {
                this.options.onmessage({ data });
              } catch (error) {
                console.error("Error processing SSE message:", error);
              }
            }
          }
        }
      }
    } catch (error) {
      if (this.options.onerror && !this.controller?.signal.aborted) {
        this.options.onerror(error);
      }
    }
  }
}

export const useHoustonChatSSENew = () => {
  const { accessToken } = useAuth();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [streamingState, setStreamingState] = useState<StreamingState>({
    isStreaming: false,
    currentContent: "",
    error: null,
    status: "idle",
    loadingMessage: "",
  });

  const sseClientRef = useRef<CustomSSEClient | null>(null);

  const sendMessage = useCallback(
    async ({
      conversationId,
      message,
      settings,
    }: {
      conversationId: string;
      message: string;
      settings?: ChatSettings;
    }) => {
      try {
        setStreamingState({
          isStreaming: true,
          currentContent: "",
          loadingMessage: "Houston is typing...",
          error: null,
          status: "connecting",
        });

        // Add user message immediately
        const userMessage: ChatMessage = {
          id: Date.now().toString(),
          content: message,
          role: "user",
          createdAt: new Date().toISOString(),
        };
        setMessages((prev) => [...prev, userMessage]);

        console.log("Sending message with token:", accessToken);

        // Create SSE client
        const sseClient = new CustomSSEClient(
          `${process.env.NEXT_PUBLIC_API_URL}/api/chat/conversations/${conversationId}/messages/stream`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${accessToken}`,
            },
            body: JSON.stringify({ message, settings }),
            onopen: () => {
              console.log("SSE Connected");
              setStreamingState((prev) => ({ ...prev, status: "connected" }));
            },
            onmessage: (event: { data: string }) => {
              try {
                console.log("SSE Message received:", event.data);
                const data = JSON.parse(event.data);

                switch (data.type) {
                  case "connected":
                    console.log("SSE connection confirmed");
                    break;

                  case "content":
                    setStreamingState((prev) => ({
                      ...prev,
                      currentContent:
                        prev.currentContent + (data.content || ""),
                    }));
                    break;

                  case "tool_calls_started":
                    setStreamingState((prev) => ({
                      ...prev,
                      loadingMessage:
                        data.content || data.status || "Processing...",
                      status: "tool_calls",
                    }));
                    break;

                  case "completed":
                    // Add assistant message
                    const assistantMessage: ChatMessage = {
                      id: data.data?.id || Date.now().toString(),
                      content:
                        data.data?.content || streamingState.currentContent,
                      role: "assistant",
                      createdAt:
                        data.data?.createdAt || new Date().toISOString(),
                      references: data.data?.references || [],
                      annotations: data.data?.annotations || [],
                    };

                    setMessages((prev) => [...prev, assistantMessage]);
                    setStreamingState({
                      loadingMessage: "",
                      isStreaming: false,
                      currentContent: "",
                      error: null,
                      status: "completed",
                    });
                    break;

                  case "error":
                    setStreamingState((prev) => ({
                      ...prev,
                      isStreaming: false,
                      error:
                        data.content || data.data?.error || "An error occurred",
                    }));
                    break;

                  case "end":
                    break;

                  default:
                    console.log("Unknown SSE event type:", data.type);
                }
              } catch (error) {
                console.error("Error parsing SSE message:", error, event.data);
              }
            },
            onerror: (error: any) => {
              console.error("SSE Error:", error);
              setStreamingState((prev) => ({
                ...prev,
                isStreaming: false,
                error: "Connection error",
              }));
            },
          }
        );

        sseClientRef.current = sseClient;
      } catch (error) {
        console.error("Failed to send message:", error);
        setStreamingState({
          isStreaming: false,
          currentContent: "",
          error: "Failed to send message",
          status: "error",
          loadingMessage: "",
        });
      }
    },
    [accessToken, streamingState.currentContent]
  );

  const stopStreaming = useCallback(() => {
    if (sseClientRef.current) {
      sseClientRef.current = null;
      setStreamingState((prev) => ({
        ...prev,
        isStreaming: false,
      }));
    }
  }, []);

  return {
    messages,
    setMessages,
    streamingState,
    sendMessage,
    stopStreaming,
  };
};
