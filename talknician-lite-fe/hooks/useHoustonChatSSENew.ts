import { useState, useCallback, useRef, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useOrganization } from "@/contexts/OrganizationContext";
import { ChatMessage, ChatSettings, StreamingState } from "@/types/chat";
import { useChatStore } from "@/stores/useChatStore";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { createChatApiClient } from "@/utils/chatApi";

// Custom SSE client for POST requests
class CustomSSEClient {
  private controller: AbortController | null = null;
  private url: string;
  private options: any;

  constructor(url: string, options: any) {
    this.url = url;
    this.options = options;
    this.connect();
  }

  private async connect() {
    this.controller = new AbortController();

    try {
      const response = await fetch(this.url, {
        ...this.options,
        signal: this.controller.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      if (this.options.onopen) {
        this.options.onopen();
      }

      const reader = response.body?.getReader();
      const decoder = new TextDecoder();

      if (!reader) {
        throw new Error("No response body reader available");
      }

      let buffer = "";

      while (true) {
        const { done, value } = await reader.read();

        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split("\n");

        // Keep the last incomplete line in buffer
        buffer = lines.pop() || "";

        for (const line of lines) {
          if (line.startsWith("event: ")) {
            // Store event type for next data line
            continue;
          } else if (line.startsWith("data: ")) {
            const data = line.slice(6).trim();
            if (data && this.options.onmessage) {
              try {
                this.options.onmessage({ data });
              } catch (error) {
                console.error("Error processing SSE message:", error);
              }
            }
          }
        }
      }
    } catch (error) {
      if (this.options.onerror && !this.controller?.signal.aborted) {
        this.options.onerror(error);
      }
    }
  }
}

export const useHoustonChatSSENew = () => {
  const { accessToken } = useAuth();
  const { currentOrganization } = useOrganization();
  const queryClient = useQueryClient();

  // Zustand store
  const {
    currentConversation,
    messages,
    conversations,
    inputValue,
    conversationSearchQuery,
    isMobileConversationsOpen,
    chatSettings,
    currentRequestWebSearch,
    streamingState,
    setCurrentConversation,
    setMessages,
    addMessage,
    setConversations,
    addConversation,
    removeConversation,
    setInputValue,
    setConversationSearchQuery,
    setIsMobileConversationsOpen,
    setChatSettings,
    setCurrentRequestWebSearch,
    setStreamingState,
    resetMessages,
  } = useChatStore();

  const sseClientRef = useRef<CustomSSEClient | null>(null);
  const apiClient = createChatApiClient(accessToken);

  // React Query for conversations
  const conversationsQuery = useQuery({
    queryKey: ["conversations", currentOrganization?.id],
    queryFn: () => apiClient.getConversations(currentOrganization!.id),
    enabled: !!currentOrganization && !!accessToken,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // React Query for chat settings
  const chatSettingsQuery = useQuery({
    queryKey: ["chatSettings", currentOrganization?.id],
    queryFn: () => apiClient.getChatSettings(currentOrganization!.id),
    enabled: !!currentOrganization && !!accessToken,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  // Mutations
  const createConversationMutation = useMutation({
    mutationFn: () => apiClient.createConversation(),
    onSuccess: (newConversation) => {
      addConversation(newConversation);
      setCurrentConversation(newConversation);
      resetMessages();
      queryClient.invalidateQueries({ queryKey: ["conversations"] });
    },
  });

  const deleteConversationMutation = useMutation({
    mutationFn: (conversationId: string) =>
      apiClient.deleteConversation(conversationId),
    onSuccess: (_, conversationId) => {
      removeConversation(conversationId);
      if (currentConversation?.id === conversationId) {
        const remainingConversations = conversations.filter(
          (c) => c.id !== conversationId
        );
        if (remainingConversations.length > 0) {
          loadConversation(remainingConversations[0].id);
        } else {
          setCurrentConversation(null);
          resetMessages();
        }
      }
      queryClient.invalidateQueries({ queryKey: ["conversations"] });
    },
  });

  const updateChatSettingsMutation = useMutation({
    mutationFn: (settings: Partial<ChatSettings>) =>
      apiClient.updateChatSettings(currentOrganization!.id, settings),
    onSuccess: (updatedSettings) => {
      setChatSettings(updatedSettings);
      queryClient.invalidateQueries({ queryKey: ["chatSettings"] });
    },
  });

  // Load conversations effect
  useEffect(() => {
    if (conversationsQuery.data) {
      setConversations(conversationsQuery.data);
      // Auto-select first conversation if none selected
      if (conversationsQuery.data.length > 0 && !currentConversation) {
        loadConversation(conversationsQuery.data[0].id);
      }
    }
  }, [conversationsQuery.data, currentConversation]);

  // Load chat settings effect
  useEffect(() => {
    if (chatSettingsQuery.data) {
      setChatSettings(chatSettingsQuery.data);
    }
  }, [chatSettingsQuery.data]);

  // Load specific conversation
  const loadConversation = useCallback(
    async (conversationId: string) => {
      if (!accessToken) return;

      try {
        setStreamingState({ error: null });
        const { conversation, messages } = await apiClient.getConversation(
          conversationId
        );
        setCurrentConversation(conversation);
        setMessages(messages);
      } catch (error) {
        console.error("Failed to load conversation:", error);
        setStreamingState({ error: "Failed to load conversation" });
      }
    },
    [
      accessToken,
      apiClient,
      setCurrentConversation,
      setMessages,
      setStreamingState,
    ]
  );

  const sendMessage = useCallback(
    async ({
      conversationId,
      message,
      settings,
    }: {
      conversationId: string;
      message: string;
      settings?: ChatSettings;
    }) => {
      try {
        setStreamingState({
          isStreaming: true,
          currentContent: "",
          loadingMessage: "Houston is typing...",
          error: null,
          status: "connecting",
        });

        // Add user message immediately
        const userMessage: ChatMessage = {
          id: Date.now().toString(),
          content: message,
          role: "user",
          createdAt: new Date().toISOString(),
        };
        addMessage(userMessage);

        console.log("Sending message with token:", accessToken);

        // Create SSE client
        const sseClient = new CustomSSEClient(
          `${process.env.NEXT_PUBLIC_API_URL}/api/chat/conversations/${conversationId}/messages/stream`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${accessToken}`,
            },
            body: JSON.stringify({ message, settings }),
            onopen: () => {
              console.log("SSE Connected");
              setStreamingState({ status: "connected" });
            },
            onmessage: (event: { data: string }) => {
              try {
                console.log("SSE Message received:", event.data);
                const data = JSON.parse(event.data);

                switch (data.type) {
                  case "connected":
                    console.log("SSE connection confirmed");
                    break;

                  case "content":
                    setStreamingState({
                      currentContent:
                        streamingState.currentContent + (data.content || ""),
                    });
                    break;

                  case "tool_calls_started":
                    setStreamingState({
                      loadingMessage:
                        data.content || data.status || "Processing...",
                      status: "tool_calls",
                    });
                    break;

                  case "completed":
                    // Add assistant message
                    const assistantMessage: ChatMessage = {
                      id: data.data?.id || Date.now().toString(),
                      content:
                        data.data?.content || streamingState.currentContent,
                      role: "assistant",
                      createdAt:
                        data.data?.createdAt || new Date().toISOString(),
                      references: data.data?.references || [],
                      annotations: data.data?.annotations || [],
                    };

                    addMessage(assistantMessage);
                    setStreamingState({
                      loadingMessage: "",
                      isStreaming: false,
                      currentContent: "",
                      error: null,
                      status: "completed",
                    });
                    break;

                  case "error":
                    setStreamingState({
                      isStreaming: false,
                      error:
                        data.content || data.data?.error || "An error occurred",
                    });
                    break;

                  case "end":
                    break;

                  default:
                    console.log("Unknown SSE event type:", data.type);
                }
              } catch (error) {
                console.error("Error parsing SSE message:", error, event.data);
              }
            },
            onerror: (error: any) => {
              console.error("SSE Error:", error);
              setStreamingState({
                isStreaming: false,
                error: "Connection error",
              });
            },
          }
        );

        sseClientRef.current = sseClient;
      } catch (error) {
        console.error("Failed to send message:", error);
        setStreamingState({
          isStreaming: false,
          currentContent: "",
          error: "Failed to send message",
          status: "error",
          loadingMessage: "",
        });
      }
    },
    [accessToken, streamingState.currentContent]
  );

  const stopStreaming = useCallback(() => {
    if (sseClientRef.current) {
      sseClientRef.current = null;
      setStreamingState({
        isStreaming: false,
      });
    }
  }, [setStreamingState]);

  // Filter conversations based on search
  const filteredConversations = conversations.filter((conv) =>
    conv.title.toLowerCase().includes(conversationSearchQuery.toLowerCase())
  );

  return {
    // State
    currentConversation,
    messages,
    conversations: filteredConversations,
    inputValue,
    conversationSearchQuery,
    isMobileConversationsOpen,
    chatSettings,
    currentRequestWebSearch,
    streamingState,

    // Loading states
    isLoadingConversations: conversationsQuery.isLoading,
    isLoadingSettings: chatSettingsQuery.isLoading,

    // Actions
    setInputValue,
    setConversationSearchQuery,
    setIsMobileConversationsOpen,
    setCurrentRequestWebSearch,
    loadConversation,
    createNewConversation: () => createConversationMutation.mutate(),
    deleteConversation: (id: string) => deleteConversationMutation.mutate(id),
    updateChatSettings: (settings: Partial<ChatSettings>) =>
      updateChatSettingsMutation.mutate(settings),
    sendMessage,
    stopStreaming,
  };
};
