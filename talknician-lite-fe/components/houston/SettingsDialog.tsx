"use client";

import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { ChatSettings } from "@/types/chat";

interface SettingsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  settings: ChatSettings | null;
  onSave: (settings: Partial<ChatSettings>) => void;
  isLoading?: boolean;
}

export function SettingsDialog({
  open,
  onOpenChange,
  settings,
  onSave,
  isLoading = false,
}: SettingsDialogProps) {
  const [localSettings, setLocalSettings] = useState<Partial<ChatSettings>>({});

  useEffect(() => {
    if (settings) {
      setLocalSettings(settings);
    }
  }, [settings]);

  const handleSave = () => {
    onSave(localSettings);
    onOpenChange(false);
  };

  const updateSetting = (key: keyof ChatSettings, value: any) => {
    setLocalSettings((prev) => ({ ...prev, [key]: value }));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Chat Settings</DialogTitle>
          <DialogDescription>
            Customize your Houston AI assistant behavior and preferences.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Model Settings */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Model Configuration</h3>
            
            <div className="space-y-2">
              <Label htmlFor="model">AI Model</Label>
              <Select
                value={localSettings.model || "gpt-4"}
                onValueChange={(value) => updateSetting("model", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select model" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="gpt-4">GPT-4</SelectItem>
                  <SelectItem value="gpt-4-turbo">GPT-4 Turbo</SelectItem>
                  <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="temperature">
                Temperature: {localSettings.temperature || 0.7}
              </Label>
              <Slider
                id="temperature"
                min={0}
                max={2}
                step={0.1}
                value={[localSettings.temperature || 0.7]}
                onValueChange={([value]) => updateSetting("temperature", value)}
                className="w-full"
              />
              <p className="text-xs text-slate-500">
                Lower values make responses more focused, higher values more creative
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="maxTokens">
                Max Tokens: {localSettings.maxTokens || 2000}
              </Label>
              <Slider
                id="maxTokens"
                min={100}
                max={4000}
                step={100}
                value={[localSettings.maxTokens || 2000]}
                onValueChange={([value]) => updateSetting("maxTokens", value)}
                className="w-full"
              />
            </div>
          </div>

          <Separator />

          {/* Feature Settings */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Features</h3>
            
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="fileSearch">File Search</Label>
                <p className="text-xs text-slate-500">
                  Enable searching through uploaded documents
                </p>
              </div>
              <Switch
                id="fileSearch"
                checked={localSettings.fileSearchEnabled ?? true}
                onCheckedChange={(checked) => updateSetting("fileSearchEnabled", checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="webSearch">Web Search</Label>
                <p className="text-xs text-slate-500">
                  Allow AI to search the web for current information
                </p>
              </div>
              <Switch
                id="webSearch"
                checked={localSettings.webSearchEnabled ?? false}
                onCheckedChange={(checked) => updateSetting("webSearchEnabled", checked)}
              />
            </div>
          </div>

          <Separator />

          {/* System Prompt */}
          <div className="space-y-2">
            <Label htmlFor="systemPrompt">System Prompt</Label>
            <Textarea
              id="systemPrompt"
              placeholder="Enter custom system prompt..."
              value={localSettings.systemPrompt || ""}
              onChange={(e) => updateSetting("systemPrompt", e.target.value)}
              rows={4}
            />
            <p className="text-xs text-slate-500">
              Customize how the AI assistant behaves and responds
            </p>
          </div>
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={isLoading}>
            {isLoading ? "Saving..." : "Save Changes"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
