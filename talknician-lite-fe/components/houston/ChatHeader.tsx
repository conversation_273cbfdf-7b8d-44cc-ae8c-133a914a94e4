"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Menu, Settings, Zap, StopCircle } from "lucide-react";
import { Conversation, StreamingState } from "@/types/chat";

interface ChatHeaderProps {
  currentConversation: Conversation | null;
  streamingState: StreamingState;
  onMobileMenuToggle: () => void;
  onSettingsClick: () => void;
  onStopStreaming: () => void;
}

export function ChatHeader({
  currentConversation,
  streamingState,
  onMobileMenuToggle,
  onSettingsClick,
  onStopStreaming,
}: ChatHeaderProps) {
  return (
    <div className="border-b border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800 px-4 py-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          {/* Mobile menu button */}
          <Button
            variant="ghost"
            size="sm"
            className="md:hidden"
            onClick={onMobileMenuToggle}
          >
            <Menu className="h-4 w-4" />
          </Button>

          {/* Conversation title */}
          <div className="flex items-center space-x-2">
            <h1 className="text-lg font-semibold text-slate-900 dark:text-white">
              {currentConversation?.title || "New Conversation"}
            </h1>
            
            {/* Status indicators */}
            {streamingState.isStreaming && (
              <Badge variant="secondary" className="text-xs">
                <Zap className="w-3 h-3 mr-1" />
                Thinking...
              </Badge>
            )}
            
            {streamingState.status === "tool_calls" && (
              <Badge variant="outline" className="text-xs">
                <Settings className="w-3 h-3 mr-1" />
                Processing...
              </Badge>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* Stop streaming button */}
          {streamingState.isStreaming && (
            <Button
              variant="outline"
              size="sm"
              onClick={onStopStreaming}
              className="text-red-600 border-red-200 hover:bg-red-50"
            >
              <StopCircle className="w-4 h-4 mr-1" />
              Stop
            </Button>
          )}

          {/* Settings button */}
          <Button variant="ghost" size="sm" onClick={onSettingsClick}>
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
