"use client";

import React, { useEffect, useRef } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, AlertCircle } from "lucide-react";
import { ChatMessage } from "@/components/houston/ChatMessage";
import { ChatMessage as ChatMessageType, StreamingState } from "@/types/chat";

interface MessagesListProps {
  messages: ChatMessageType[];
  streamingState: StreamingState;
  className?: string;
}

export function MessagesList({ messages, streamingState, className }: MessagesListProps) {
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages, streamingState.currentContent]);

  return (
    <ScrollArea ref={scrollAreaRef} className={`flex-1 ${className}`}>
      <div className="p-4 space-y-4">
        {messages.length === 0 && !streamingState.isStreaming ? (
          <div className="flex flex-col items-center justify-center h-64 text-center">
            <div className="text-6xl mb-4">🚀</div>
            <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-2">
              Welcome to Houston
            </h3>
            <p className="text-slate-600 dark:text-slate-400 max-w-md">
              Your AI assistant is ready to help. Ask questions about your documents,
              get insights, or start a conversation.
            </p>
          </div>
        ) : (
          <>
            {messages.map((message) => (
              <ChatMessage key={message.id} message={message} />
            ))}

            {/* Streaming message */}
            {streamingState.isStreaming && (
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 rounded-full bg-indigo-100 dark:bg-indigo-900 flex items-center justify-center">
                    <Loader2 className="w-4 h-4 text-indigo-600 dark:text-indigo-400 animate-spin" />
                  </div>
                </div>
                <div className="flex-1 space-y-2">
                  {streamingState.loadingMessage && (
                    <div className="text-sm text-slate-600 dark:text-slate-400 italic">
                      {streamingState.loadingMessage}
                    </div>
                  )}
                  {streamingState.currentContent && (
                    <div className="prose prose-sm dark:prose-invert max-w-none">
                      <div className="whitespace-pre-wrap">
                        {streamingState.currentContent}
                        <span className="inline-block w-2 h-4 bg-indigo-500 animate-pulse ml-1" />
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Error state */}
            {streamingState.error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{streamingState.error}</AlertDescription>
              </Alert>
            )}
          </>
        )}
        <div ref={messagesEndRef} />
      </div>
    </ScrollArea>
  );
}
