"use client";

import React, { useState } from "react";
import { X } from "lucide-react";
import { useHoustonChatSSE } from "@/hooks/useHoustonChatSSE";
import { ConversationsSidebar } from "@/components/houston/ConversationsSidebar";
import { ChatHeader } from "@/components/houston/ChatHeader";
import { MessagesList } from "@/components/houston/MessagesList";
import { ChatInput } from "@/components/houston/ChatInput";
import { SettingsDialog } from "@/components/houston/SettingsDialog";

export default function HoustonPage() {
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);

  // Use the integrated hook that combines SSE, Zustand, and React Query
  const {
    // State
    currentConversation,
    messages,
    conversations,
    inputValue,
    conversationSearchQuery,
    isMobileConversationsOpen,
    chatSettings,
    currentRequestWebSearch,
    streamingState,

    // Loading states
    isLoadingConversations,
    isLoadingSettings,

    // Actions
    setInputValue,
    setConversationSearchQuery,
    setIsMobileConversationsOpen,
    setCurrentRequestWebSearch,
    loadConversation,
    createNewConversation,
    deleteConversation,
    updateChatSettings,
    sendMessage,
    stopStreaming,
  } = useHoustonChatSSE();

  const handleSendMessage = async (message: string) => {
    if (!currentConversation) {
      // Create new conversation first, then send message
      createNewConversation();
      return;
    }

    await sendMessage({
      conversationId: currentConversation.id,
      message,
      settings: {
        ...chatSettings,
        enableWebSearch: currentRequestWebSearch,
      },
    });
  };

  const handleSend = () => {
    setInputValue("");
    if (inputValue.trim()) {
      handleSendMessage(inputValue);
    }
  };

  return (
    <div className="flex h-screen bg-slate-50 dark:bg-slate-900">
      {/* Desktop Sidebar */}
      <div className="hidden md:flex md:w-80 md:flex-col">
        <ConversationsSidebar
          conversations={conversations}
          currentConversation={currentConversation}
          conversationSearchQuery={conversationSearchQuery}
          isLoading={isLoadingConversations}
          onSearchChange={setConversationSearchQuery}
          onConversationSelect={loadConversation}
          onCreateNew={createNewConversation}
          onDeleteConversation={deleteConversation}
          onSettingsClick={() => setIsSettingsOpen(true)}
        />
      </div>

      {/* Mobile Sidebar */}
      {isMobileConversationsOpen && (
        <div className="fixed inset-0 z-50 md:hidden">
          <div
            className="fixed inset-0 bg-black/50"
            onClick={() => setIsMobileConversationsOpen(false)}
          />
          <div className="fixed left-0 top-0 h-full w-80 bg-white dark:bg-slate-800">
            <div className="flex items-center justify-between p-4 border-b">
              <h2 className="text-lg font-semibold">Conversations</h2>
              <button
                onClick={() => setIsMobileConversationsOpen(false)}
                className="p-2 hover:bg-slate-100 dark:hover:bg-slate-700 rounded"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
            <ConversationsSidebar
              conversations={conversations}
              currentConversation={currentConversation}
              conversationSearchQuery={conversationSearchQuery}
              isLoading={isLoadingConversations}
              onSearchChange={setConversationSearchQuery}
              onConversationSelect={(id) => {
                loadConversation(id);
                setIsMobileConversationsOpen(false);
              }}
              onCreateNew={() => {
                createNewConversation();
                setIsMobileConversationsOpen(false);
              }}
              onDeleteConversation={deleteConversation}
              onSettingsClick={() => {
                setIsSettingsOpen(true);
                setIsMobileConversationsOpen(false);
              }}
            />
          </div>
        </div>
      )}

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        <ChatHeader
          currentConversation={currentConversation}
          streamingState={streamingState}
          onMobileMenuToggle={() => setIsMobileConversationsOpen(true)}
          onSettingsClick={() => setIsSettingsOpen(true)}
          onStopStreaming={stopStreaming}
        />

        <MessagesList
          messages={messages}
          streamingState={streamingState}
          className="flex-1"
        />

        <ChatInput
          value={inputValue}
          onChange={setInputValue}
          onSend={handleSend}
          onStop={stopStreaming}
          isStreaming={streamingState.isStreaming}
          webSearchEnabled={currentRequestWebSearch}
          onWebSearchToggle={setCurrentRequestWebSearch}
        />
      </div>

      {/* Settings Dialog */}
      <SettingsDialog
        open={isSettingsOpen}
        onOpenChange={setIsSettingsOpen}
        settings={chatSettings}
        onSave={updateChatSettings}
        isLoading={isLoadingSettings}
      />
    </div>
  );
}
