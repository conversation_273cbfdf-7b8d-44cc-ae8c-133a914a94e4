"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useHoustonChatSSENew } from "@/hooks/useHoustonChatSSENew";
import { useAuth } from "@/contexts/AuthContext";

export default function TestSSEPage() {
  const { accessToken, isAuthenticated } = useAuth();
  const { messages, streamingState, sendMessage, stopStreaming } =
    useHoustonChatSSENew();
  const [inputValue, setInputValue] = useState("");
  const [conversationId, setConversationId] = useState(
    "cmcfjo9ne0005z8ameiog1f0s"
  );

  const handleSendMessage = async () => {
    if (!inputValue.trim() || !conversationId.trim()) return;

    await sendMessage({
      conversationId,
      message: inputValue.trim(),
      settings: {
        enableWebSearch: false,
      },
    });

    setInputValue("");
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="container mx-auto p-4">
        <Card>
          <CardHeader>
            <CardTitle>SSE Test - Authentication Required</CardTitle>
          </CardHeader>
          <CardContent>
            <p>Please log in to test the SSE functionality.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle>SSE Chat Test</CardTitle>
          <p className="text-sm text-gray-600">
            Testing the new custom SSE implementation
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Connection Info */}
          <div className="bg-black-100 p-3 rounded">
            <p>
              <strong>Auth Token:</strong>{" "}
              {accessToken ? "✅ Present" : "❌ Missing"}
            </p>
            <p>
              <strong>Status:</strong> {streamingState.status}
            </p>
            <p>
              <strong>Streaming:</strong>{" "}
              {streamingState.isStreaming ? "🔄 Yes" : "⏹️ No"}
            </p>
            {streamingState.error && (
              <p className="text-red-600">
                <strong>Error:</strong> {streamingState.error}
              </p>
            )}
            {streamingState.loadingMessage && (
              <p className="text-blue-600">
                <strong>Loading:</strong> {streamingState.loadingMessage}
              </p>
            )}
          </div>

          {/* Conversation ID Input */}
          <div>
            <label className="block text-sm font-medium mb-1">
              Conversation ID:
            </label>
            <Input
              value={conversationId}
              onChange={(e) => setConversationId(e.target.value)}
              placeholder="Enter conversation ID"
            />
          </div>

          {/* Messages Display */}
          <div className="border rounded-lg p-4 h-96 overflow-y-auto bg-gray-50">
            {messages.length === 0 ? (
              <p className="text-gray-500 text-center">
                No messages yet. Send a message to test SSE!
              </p>
            ) : (
              <div className="space-y-3">
                {messages.map((message, index) => (
                  <div
                    key={index}
                    className={`p-3 rounded-lg ${
                      message.role === "user"
                        ? "bg-black ml-8"
                        : "bg-black mr-8 border"
                    }`}
                  >
                    <div className="flex justify-between items-start mb-1">
                      <span className="font-medium text-sm">
                        {message.role === "user" ? "You" : "Houston"}
                      </span>
                      <span className="text-xs text-gray-500">
                        {message.createdAt
                          ? new Date(message.createdAt).toLocaleTimeString()
                          : ""}
                      </span>
                    </div>
                    <p className="whitespace-pre-wrap">{message.content}</p>
                    {message.references && message.references.length > 0 && (
                      <div className="mt-2 text-xs text-gray-600">
                        <strong>References:</strong>{" "}
                        {message.references.map((ref) => ref.title).join(", ")}
                      </div>
                    )}
                  </div>
                ))}

                {streamingState.isStreaming && (
                  <div
                    className={`p-3 rounded-lg ${
                      streamingState.isStreaming
                        ? "bg-black ml-8"
                        : "bg-black mr-8 border"
                    }`}
                  >
                    <div className="flex justify-between items-start mb-1">
                      <span className="font-medium text-sm">Houston</span>
                      <span className="text-xs text-gray-500">Now</span>
                    </div>
                    <p className="whitespace-pre-wrap">
                      {streamingState.currentContent}
                    </p>
                  </div>
                )}
                {/* Streaming Content */}
                {streamingState.isStreaming &&
                  streamingState.currentContent && (
                    <div className="p-3 rounded-lg bg-yellow-50 mr-8 border border-yellow-200">
                      <div className="flex justify-between items-start mb-1">
                        <span className="font-medium text-sm">
                          Houston (streaming...)
                        </span>
                        <div className="flex space-x-1">
                          <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce" />
                          <div
                            className="w-1 h-1 bg-blue-500 rounded-full animate-bounce"
                            style={{ animationDelay: "0.1s" }}
                          />
                          <div
                            className="w-1 h-1 bg-blue-500 rounded-full animate-bounce"
                            style={{ animationDelay: "0.2s" }}
                          />
                        </div>
                      </div>
                      <p className="whitespace-pre-wrap">
                        {streamingState.currentContent}
                      </p>
                    </div>
                  )}
              </div>
            )}
          </div>

          {/* Message Input */}
          <div className="flex gap-2">
            <Input
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type your message..."
              disabled={streamingState.isStreaming}
              className="flex-1"
            />
            {streamingState.isStreaming ? (
              <Button onClick={stopStreaming} variant="outline">
                Stop
              </Button>
            ) : (
              <Button
                onClick={handleSendMessage}
                disabled={!inputValue.trim() || !conversationId.trim()}
              >
                Send
              </Button>
            )}
          </div>

          {/* Debug Info */}
          <details className="text-xs">
            <summary className="cursor-pointer font-medium">Debug Info</summary>
            <pre className="mt-2 p-2 bg-black rounded overflow-auto">
              {JSON.stringify(
                {
                  messagesCount: messages.length,
                  streamingState,
                  hasToken: !!accessToken,
                  conversationId,
                },
                null,
                2
              )}
            </pre>
          </details>
        </CardContent>
      </Card>
    </div>
  );
}
